{"ast": null, "code": "import axios from 'axios';\nclass HoroscopeService {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n    this.cache = new Map();\n    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n  }\n\n  // Get cache-busting headers\n  getCacheBustingHeaders() {\n    return {\n      'Cache-Control': 'no-cache, no-store, must-revalidate',\n      'Pragma': 'no-cache',\n      'Expires': '0',\n      'X-Requested-With': 'XMLHttpRequest',\n      'X-Cache-Buster': Date.now().toString()\n    };\n  }\n\n  // Get cached horoscope if it exists and is not expired\n  getCachedHoroscope(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    if (this.cache.has(cacheKey)) {\n      const cached = this.cache.get(cacheKey);\n      if (Date.now() - cached.timestamp < this.cacheExpiry) {\n        return cached.data;\n      } else {\n        this.cache.delete(cacheKey);\n      }\n    }\n    return null;\n  }\n\n  // Cache horoscope data\n  cacheHoroscope(signId, horoscope) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    this.cache.set(cacheKey, {\n      data: horoscope,\n      timestamp: Date.now()\n    });\n  }\n\n  // Set cached horoscope data (alias for cacheHoroscope)\n  setCachedHoroscope(signId, data, expiryHours = 24) {\n    this.cacheHoroscope(signId, data, expiryHours);\n  }\n\n  // Helper method to get sign in Sinhala\n  getSignSinhala(signId) {\n    const signMap = {\n      'aries': 'මේෂ',\n      'taurus': 'වෘෂභ',\n      'gemini': 'මිථුන',\n      'cancer': 'කටක',\n      'leo': 'සිංහ',\n      'virgo': 'කන්‍යා',\n      'libra': 'තුලා',\n      'scorpio': 'වෘශ්චික',\n      'sagittarius': 'ධනු',\n      'capricorn': 'මකර',\n      'aquarius': 'කුම්භ',\n      'pisces': 'මීන'\n    };\n    return signMap[signId] || signId;\n  }\n\n  // Get horoscope from backend API\n  async getHoroscopeFromAPI(signId) {\n    try {\n      const response = await axios.get(`${this.baseURL}/horoscope/${signId}`, {\n        timeout: 10000,\n        // 10 second timeout\n        headers: this.getCacheBustingHeaders()\n      });\n      if (response.data && response.data.success) {\n        return response.data.data;\n      } else {\n        throw new Error('Invalid response from API');\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3;\n      console.error('API error details:', {\n        message: error.message,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data\n      });\n      throw error;\n    }\n  }\n\n  // Get specific category from backend API\n  async getCategoryFromAPI(signId, category) {\n    try {\n      const response = await axios.get(`${this.baseURL}/horoscope/${signId}/${category}`, {\n        timeout: 10000,\n        // 10 second timeout\n        headers: this.getCacheBustingHeaders()\n      });\n      if (response.data && response.data.success) {\n        return response.data.data;\n      } else {\n        throw new Error('Invalid response from API');\n      }\n    } catch (error) {\n      var _error$response4, _error$response5, _error$response6;\n      console.error('API error details:', {\n        message: error.message,\n        status: (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status,\n        statusText: (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.statusText,\n        data: (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data\n      });\n      throw error;\n    }\n  }\n\n  // Main method to get horoscope\n  async getHoroscope(signId) {\n    try {\n      // Check cache first\n      const cached = this.getCachedHoroscope(signId);\n      if (cached) {\n        return cached;\n      }\n\n      // Try to get from API\n      try {\n        const apiData = await this.getHoroscopeFromAPI(signId);\n        this.cacheHoroscope(signId, apiData);\n        return apiData;\n      } catch (apiError) {\n        console.warn('API failed, using fallback:', apiError.message);\n        // Return fallback data\n        const signSinhala = this.getSignSinhala(signId);\n        const fallbackData = this.generateFallbackHoroscope(signSinhala);\n        return fallbackData;\n      }\n    } catch (error) {\n      console.error('Error getting horoscope:', error);\n      throw error;\n    }\n  }\n\n  // Fallback horoscope generation - personalized content\n  generateFallbackHoroscope(signSinhala) {\n    const personalizedContent = {\n      love: 'අද දිනය ඔබේ ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ශක්තිමත් දිනයක් වනු ඇත. ඔබේ හදවතේ ඇති ධනාත්මක ශක්තිය අන්‍යයන් වෙත ප්‍රකාශ වනු ඇත.',\n      career: 'ඔබේ වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව ගුණාංග ප්‍රදර්ශනය කිරීමට අද හොඳ අවස්ථාවක්. ඔබේ කුසලතා සහ දක්ෂතා අන්‍යයන් විසින් පිළිගනු ලැබේ.',\n      health: 'ඔබේ ශාරීරික ශක්තිය සහ ක්‍රියාශීලිත්වය අද ඉහළ මට්ටමක පවතී. ඔබේ සෞඛ්‍යයට අවශ්‍ය සැලකිල්ල ලබා දෙන්න.',\n      finance: 'ඔබේ මූල්‍ය කටයුතුවල නව ආයෝජන අවස්ථා සොයා ගැනීමට අද හොඳ කාලයකි. ඔබේ ආර්ථික ස්ථාවරත්වය වැඩි දියුණු වනු ඇත.',\n      general: 'ඔබේ සාමාන්‍ය ජීවිතයේ ධනාත්මක ශක්තිය සහ සාර්ථකත්වය අද අපේක්ෂා කරන්න. ඔබේ අභිලාෂයන් සාක්ෂාත් කර ගැනීමට හොඳ දිනයකි.'\n    };\n    return {\n      date: new Date().toLocaleDateString('si-LK'),\n      categories: personalizedContent\n    };\n  }\n}\nconst horoscopeService = new HoroscopeService();\nexport default horoscopeService;", "map": {"version": 3, "names": ["axios", "HoroscopeService", "constructor", "baseURL", "process", "env", "REACT_APP_API_URL", "cache", "Map", "cacheExpiry", "getCacheBustingHeaders", "Date", "now", "toString", "getCachedHoroscope", "signId", "today", "toDateString", "cache<PERSON>ey", "has", "cached", "get", "timestamp", "data", "delete", "cacheHoroscope", "horoscope", "set", "setCachedHoroscope", "expiryHours", "getSignSinhala", "signMap", "getHoroscopeFromAPI", "response", "timeout", "headers", "success", "Error", "error", "_error$response", "_error$response2", "_error$response3", "console", "message", "status", "statusText", "getCategoryFromAPI", "category", "_error$response4", "_error$response5", "_error$response6", "getHoroscope", "apiData", "apiError", "warn", "signSinhala", "fallbackD<PERSON>", "generateFallbackHoroscope", "personalizedContent", "love", "career", "health", "finance", "general", "date", "toLocaleDateString", "categories", "horoscopeService"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/services/HoroscopeService.js"], "sourcesContent": ["import axios from 'axios';\n\nclass HoroscopeService {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n    this.cache = new Map();\n    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n  }\n\n  // Get cache-busting headers\n  getCacheBustingHeaders() {\n    return {\n      'Cache-Control': 'no-cache, no-store, must-revalidate',\n      'Pragma': 'no-cache',\n      'Expires': '0',\n      'X-Requested-With': 'XMLHttpRequest',\n      'X-Cache-Buster': Date.now().toString()\n    };\n  }\n\n  // Get cached horoscope if it exists and is not expired\n  getCachedHoroscope(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    \n    if (this.cache.has(cacheKey)) {\n      const cached = this.cache.get(cacheKey);\n      if (Date.now() - cached.timestamp < this.cacheExpiry) {\n        return cached.data;\n      } else {\n        this.cache.delete(cacheKey);\n      }\n    }\n    \n    return null;\n  }\n\n  // Cache horoscope data\n  cacheHoroscope(signId, horoscope) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    \n    this.cache.set(cacheKey, {\n      data: horoscope,\n      timestamp: Date.now()\n    });\n  }\n\n  // Set cached horoscope data (alias for cacheHoroscope)\n  setCachedHoroscope(signId, data, expiryHours = 24) {\n    this.cacheHoroscope(signId, data, expiryHours);\n  }\n\n  // Helper method to get sign in Sinhala\n  getSignSinhala(signId) {\n    const signMap = {\n      'aries': 'මේෂ',\n      'taurus': 'වෘෂභ',\n      'gemini': 'මිථුන',\n      'cancer': 'කටක',\n      'leo': 'සිංහ',\n      'virgo': 'කන්‍යා',\n      'libra': 'තුලා',\n      'scorpio': 'වෘශ්චික',\n      'sagittarius': 'ධනු',\n      'capricorn': 'මකර',\n      'aquarius': 'කුම්භ',\n      'pisces': 'මීන'\n    };\n    return signMap[signId] || signId;\n  }\n\n  // Get horoscope from backend API\n  async getHoroscopeFromAPI(signId) {\n    try {\n      const response = await axios.get(`${this.baseURL}/horoscope/${signId}`, {\n        timeout: 10000, // 10 second timeout\n        headers: this.getCacheBustingHeaders()\n      });\n\n      if (response.data && response.data.success) {\n        return response.data.data;\n      } else {\n        throw new Error('Invalid response from API');\n      }\n    } catch (error) {\n      console.error('API error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data\n      });\n      throw error;\n    }\n  }\n\n  // Get specific category from backend API\n  async getCategoryFromAPI(signId, category) {\n    try {\n      const response = await axios.get(`${this.baseURL}/horoscope/${signId}/${category}`, {\n        timeout: 10000, // 10 second timeout\n        headers: this.getCacheBustingHeaders()\n      });\n\n      if (response.data && response.data.success) {\n        return response.data.data;\n      } else {\n        throw new Error('Invalid response from API');\n      }\n    } catch (error) {\n      console.error('API error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data\n      });\n      throw error;\n    }\n  }\n\n  // Main method to get horoscope\n  async getHoroscope(signId) {\n    try {\n      // Check cache first\n      const cached = this.getCachedHoroscope(signId);\n      if (cached) {\n        return cached;\n      }\n\n      // Try to get from API\n      try {\n        const apiData = await this.getHoroscopeFromAPI(signId);\n        this.cacheHoroscope(signId, apiData);\n        return apiData;\n      } catch (apiError) {\n        console.warn('API failed, using fallback:', apiError.message);\n        // Return fallback data\n        const signSinhala = this.getSignSinhala(signId);\n        const fallbackData = this.generateFallbackHoroscope(signSinhala);\n        return fallbackData;\n      }\n    } catch (error) {\n      console.error('Error getting horoscope:', error);\n      throw error;\n    }\n  }\n\n  // Fallback horoscope generation - personalized content\n  generateFallbackHoroscope(signSinhala) {\n    const personalizedContent = {\n      love: 'අද දිනය ඔබේ ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ශක්තිමත් දිනයක් වනු ඇත. ඔබේ හදවතේ ඇති ධනාත්මක ශක්තිය අන්‍යයන් වෙත ප්‍රකාශ වනු ඇත.',\n      career: 'ඔබේ වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව ගුණාංග ප්‍රදර්ශනය කිරීමට අද හොඳ අවස්ථාවක්. ඔබේ කුසලතා සහ දක්ෂතා අන්‍යයන් විසින් පිළිගනු ලැබේ.',\n      health: 'ඔබේ ශාරීරික ශක්තිය සහ ක්‍රියාශීලිත්වය අද ඉහළ මට්ටමක පවතී. ඔබේ සෞඛ්‍යයට අවශ්‍ය සැලකිල්ල ලබා දෙන්න.',\n      finance: 'ඔබේ මූල්‍ය කටයුතුවල නව ආයෝජන අවස්ථා සොයා ගැනීමට අද හොඳ කාලයකි. ඔබේ ආර්ථික ස්ථාවරත්වය වැඩි දියුණු වනු ඇත.',\n      general: 'ඔබේ සාමාන්‍ය ජීවිතයේ ධනාත්මක ශක්තිය සහ සාර්ථකත්වය අද අපේක්ෂා කරන්න. ඔබේ අභිලාෂයන් සාක්ෂාත් කර ගැනීමට හොඳ දිනයකි.'\n    };\n\n    return {\n      date: new Date().toLocaleDateString('si-LK'),\n      categories: personalizedContent\n    };\n  }\n}\n\nconst horoscopeService = new HoroscopeService();\nexport default horoscopeService;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,gBAAgB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;IAC3E,IAAI,CAACC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,WAAW,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;EAC1C;;EAEA;EACAC,sBAAsBA,CAAA,EAAG;IACvB,OAAO;MACL,eAAe,EAAE,qCAAqC;MACtD,QAAQ,EAAE,UAAU;MACpB,SAAS,EAAE,GAAG;MACd,kBAAkB,EAAE,gBAAgB;MACpC,gBAAgB,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC;IACxC,CAAC;EACH;;EAEA;EACAC,kBAAkBA,CAACC,MAAM,EAAE;IACzB,MAAMC,KAAK,GAAG,IAAIL,IAAI,CAAC,CAAC,CAACM,YAAY,CAAC,CAAC;IACvC,MAAMC,QAAQ,GAAG,GAAGH,MAAM,IAAIC,KAAK,EAAE;IAErC,IAAI,IAAI,CAACT,KAAK,CAACY,GAAG,CAACD,QAAQ,CAAC,EAAE;MAC5B,MAAME,MAAM,GAAG,IAAI,CAACb,KAAK,CAACc,GAAG,CAACH,QAAQ,CAAC;MACvC,IAAIP,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGQ,MAAM,CAACE,SAAS,GAAG,IAAI,CAACb,WAAW,EAAE;QACpD,OAAOW,MAAM,CAACG,IAAI;MACpB,CAAC,MAAM;QACL,IAAI,CAAChB,KAAK,CAACiB,MAAM,CAACN,QAAQ,CAAC;MAC7B;IACF;IAEA,OAAO,IAAI;EACb;;EAEA;EACAO,cAAcA,CAACV,MAAM,EAAEW,SAAS,EAAE;IAChC,MAAMV,KAAK,GAAG,IAAIL,IAAI,CAAC,CAAC,CAACM,YAAY,CAAC,CAAC;IACvC,MAAMC,QAAQ,GAAG,GAAGH,MAAM,IAAIC,KAAK,EAAE;IAErC,IAAI,CAACT,KAAK,CAACoB,GAAG,CAACT,QAAQ,EAAE;MACvBK,IAAI,EAAEG,SAAS;MACfJ,SAAS,EAAEX,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC,CAAC;EACJ;;EAEA;EACAgB,kBAAkBA,CAACb,MAAM,EAAEQ,IAAI,EAAEM,WAAW,GAAG,EAAE,EAAE;IACjD,IAAI,CAACJ,cAAc,CAACV,MAAM,EAAEQ,IAAI,EAAEM,WAAW,CAAC;EAChD;;EAEA;EACAC,cAAcA,CAACf,MAAM,EAAE;IACrB,MAAMgB,OAAO,GAAG;MACd,OAAO,EAAE,KAAK;MACd,QAAQ,EAAE,MAAM;MAChB,QAAQ,EAAE,OAAO;MACjB,QAAQ,EAAE,KAAK;MACf,KAAK,EAAE,MAAM;MACb,OAAO,EAAE,QAAQ;MACjB,OAAO,EAAE,MAAM;MACf,SAAS,EAAE,SAAS;MACpB,aAAa,EAAE,KAAK;MACpB,WAAW,EAAE,KAAK;MAClB,UAAU,EAAE,OAAO;MACnB,QAAQ,EAAE;IACZ,CAAC;IACD,OAAOA,OAAO,CAAChB,MAAM,CAAC,IAAIA,MAAM;EAClC;;EAEA;EACA,MAAMiB,mBAAmBA,CAACjB,MAAM,EAAE;IAChC,IAAI;MACF,MAAMkB,QAAQ,GAAG,MAAMjC,KAAK,CAACqB,GAAG,CAAC,GAAG,IAAI,CAAClB,OAAO,cAAcY,MAAM,EAAE,EAAE;QACtEmB,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE,IAAI,CAACzB,sBAAsB,CAAC;MACvC,CAAC,CAAC;MAEF,IAAIuB,QAAQ,CAACV,IAAI,IAAIU,QAAQ,CAACV,IAAI,CAACa,OAAO,EAAE;QAC1C,OAAOH,QAAQ,CAACV,IAAI,CAACA,IAAI;MAC3B,CAAC,MAAM;QACL,MAAM,IAAIc,KAAK,CAAC,2BAA2B,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdC,OAAO,CAACJ,KAAK,CAAC,oBAAoB,EAAE;QAClCK,OAAO,EAAEL,KAAK,CAACK,OAAO;QACtBC,MAAM,GAAAL,eAAA,GAAED,KAAK,CAACL,QAAQ,cAAAM,eAAA,uBAAdA,eAAA,CAAgBK,MAAM;QAC9BC,UAAU,GAAAL,gBAAA,GAAEF,KAAK,CAACL,QAAQ,cAAAO,gBAAA,uBAAdA,gBAAA,CAAgBK,UAAU;QACtCtB,IAAI,GAAAkB,gBAAA,GAAEH,KAAK,CAACL,QAAQ,cAAAQ,gBAAA,uBAAdA,gBAAA,CAAgBlB;MACxB,CAAC,CAAC;MACF,MAAMe,KAAK;IACb;EACF;;EAEA;EACA,MAAMQ,kBAAkBA,CAAC/B,MAAM,EAAEgC,QAAQ,EAAE;IACzC,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMjC,KAAK,CAACqB,GAAG,CAAC,GAAG,IAAI,CAAClB,OAAO,cAAcY,MAAM,IAAIgC,QAAQ,EAAE,EAAE;QAClFb,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE,IAAI,CAACzB,sBAAsB,CAAC;MACvC,CAAC,CAAC;MAEF,IAAIuB,QAAQ,CAACV,IAAI,IAAIU,QAAQ,CAACV,IAAI,CAACa,OAAO,EAAE;QAC1C,OAAOH,QAAQ,CAACV,IAAI,CAACA,IAAI;MAC3B,CAAC,MAAM;QACL,MAAM,IAAIc,KAAK,CAAC,2BAA2B,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAU,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdR,OAAO,CAACJ,KAAK,CAAC,oBAAoB,EAAE;QAClCK,OAAO,EAAEL,KAAK,CAACK,OAAO;QACtBC,MAAM,GAAAI,gBAAA,GAAEV,KAAK,CAACL,QAAQ,cAAAe,gBAAA,uBAAdA,gBAAA,CAAgBJ,MAAM;QAC9BC,UAAU,GAAAI,gBAAA,GAAEX,KAAK,CAACL,QAAQ,cAAAgB,gBAAA,uBAAdA,gBAAA,CAAgBJ,UAAU;QACtCtB,IAAI,GAAA2B,gBAAA,GAAEZ,KAAK,CAACL,QAAQ,cAAAiB,gBAAA,uBAAdA,gBAAA,CAAgB3B;MACxB,CAAC,CAAC;MACF,MAAMe,KAAK;IACb;EACF;;EAEA;EACA,MAAMa,YAAYA,CAACpC,MAAM,EAAE;IACzB,IAAI;MACF;MACA,MAAMK,MAAM,GAAG,IAAI,CAACN,kBAAkB,CAACC,MAAM,CAAC;MAC9C,IAAIK,MAAM,EAAE;QACV,OAAOA,MAAM;MACf;;MAEA;MACA,IAAI;QACF,MAAMgC,OAAO,GAAG,MAAM,IAAI,CAACpB,mBAAmB,CAACjB,MAAM,CAAC;QACtD,IAAI,CAACU,cAAc,CAACV,MAAM,EAAEqC,OAAO,CAAC;QACpC,OAAOA,OAAO;MAChB,CAAC,CAAC,OAAOC,QAAQ,EAAE;QACjBX,OAAO,CAACY,IAAI,CAAC,6BAA6B,EAAED,QAAQ,CAACV,OAAO,CAAC;QAC7D;QACA,MAAMY,WAAW,GAAG,IAAI,CAACzB,cAAc,CAACf,MAAM,CAAC;QAC/C,MAAMyC,YAAY,GAAG,IAAI,CAACC,yBAAyB,CAACF,WAAW,CAAC;QAChE,OAAOC,YAAY;MACrB;IACF,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF;;EAEA;EACAmB,yBAAyBA,CAACF,WAAW,EAAE;IACrC,MAAMG,mBAAmB,GAAG;MAC1BC,IAAI,EAAE,4HAA4H;MAClIC,MAAM,EAAE,8HAA8H;MACtIC,MAAM,EAAE,mGAAmG;MAC3GC,OAAO,EAAE,0GAA0G;MACnHC,OAAO,EAAE;IACX,CAAC;IAED,OAAO;MACLC,IAAI,EAAE,IAAIrD,IAAI,CAAC,CAAC,CAACsD,kBAAkB,CAAC,OAAO,CAAC;MAC5CC,UAAU,EAAER;IACd,CAAC;EACH;AACF;AAEA,MAAMS,gBAAgB,GAAG,IAAIlE,gBAAgB,CAAC,CAAC;AAC/C,eAAekE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}