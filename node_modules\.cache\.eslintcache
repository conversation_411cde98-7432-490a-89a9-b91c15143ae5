[{"C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ZodiacPage.js": "1", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\HoroscopeService.js": "2"}, {"size": 27852, "mtime": 1751912523602, "results": "3", "hashOfConfig": "4"}, {"size": 7098, "mtime": 1751912207521, "results": "5", "hashOfConfig": "4"}, {"filePath": "6", "messages": "7", "suppressedMessages": "8", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "cwcvdy", {"filePath": "9", "messages": "10", "suppressedMessages": "11", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ZodiacPage.js", ["12", "13"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\HoroscopeService.js", [], [], {"ruleId": "14", "severity": 1, "message": "15", "line": 364, "column": 6, "nodeType": "16", "endLine": 364, "endColumn": 42, "suggestions": "17"}, {"ruleId": "14", "severity": 1, "message": "18", "line": 524, "column": 6, "nodeType": "16", "endLine": 524, "endColumn": 15, "suggestions": "19"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'categories'. Either include it or remove the dependency array.", "ArrayExpression", ["20"], "React Hook useCallback has a missing dependency: 'analytics'. Either include it or remove the dependency array.", ["21"], {"desc": "22", "fix": "23"}, {"desc": "24", "fix": "25"}, "Update the dependencies array to be: [horoscope, getTranslatedCategories, categories]", {"range": "26", "text": "27"}, "Update the dependencies array to be: [analytics, sign.id]", {"range": "28", "text": "29"}, [12118, 12154], "[horoscope, getTranslatedCategories, categories]", [17306, 17315], "[analytics, sign.id]"]