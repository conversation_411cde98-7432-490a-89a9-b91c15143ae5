import axios from 'axios';

class HoroscopeService {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
    this.cache = new Map();
    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  }

  // Get cache-busting headers
  getCacheBustingHeaders() {
    return {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
      'X-Requested-With': 'XMLHttpRequest',
      'X-Cache-Buster': Date.now().toString()
    };
  }

  // Get cached horoscope if it exists and is not expired
  getCachedHoroscope(signId) {
    const today = new Date().toDateString();
    const cacheKey = `${signId}_${today}`;
    
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheExpiry) {
        return cached.data;
      } else {
        this.cache.delete(cacheKey);
      }
    }
    
    return null;
  }

  // Cache horoscope data
  cacheHoroscope(signId, horoscope) {
    const today = new Date().toDateString();
    const cacheKey = `${signId}_${today}`;
    
    this.cache.set(cacheKey, {
      data: horoscope,
      timestamp: Date.now()
    });
  }

  // Set cached horoscope data (alias for cacheHoroscope)
  setCachedHoroscope(signId, data, expiryHours = 24) {
    this.cacheHoroscope(signId, data, expiryHours);
  }

  // Helper method to get sign in Sinhala
  getSignSinhala(signId) {
    const signMap = {
      'aries': 'මේෂ',
      'taurus': 'වෘෂභ',
      'gemini': 'මිථුන',
      'cancer': 'කටක',
      'leo': 'සිංහ',
      'virgo': 'කන්‍යා',
      'libra': 'තුලා',
      'scorpio': 'වෘශ්චික',
      'sagittarius': 'ධනු',
      'capricorn': 'මකර',
      'aquarius': 'කුම්භ',
      'pisces': 'මීන'
    };
    return signMap[signId] || signId;
  }

  // Get horoscope from backend API
  async getHoroscopeFromAPI(signId) {
    try {
      const response = await axios.get(`${this.baseURL}/horoscope/${signId}`, {
        timeout: 10000, // 10 second timeout
        headers: this.getCacheBustingHeaders()
      });

      if (response.data && response.data.success) {
        return response.data.data;
      } else {
        throw new Error('Invalid response from API');
      }
    } catch (error) {
      console.error('API error details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });
      throw error;
    }
  }

  // Get specific category from backend API
  async getCategoryFromAPI(signId, category) {
    try {
      const response = await axios.get(`${this.baseURL}/horoscope/${signId}/${category}`, {
        timeout: 10000, // 10 second timeout
        headers: this.getCacheBustingHeaders()
      });

      if (response.data && response.data.success) {
        return response.data.data;
      } else {
        throw new Error('Invalid response from API');
      }
    } catch (error) {
      console.error('API error details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });
      throw error;
    }
  }

  // Main method to get horoscope
  async getHoroscope(signId) {
    try {
      // Check cache first
      const cached = this.getCachedHoroscope(signId);
      if (cached) {
        return cached;
      }

      // Try to get from API
      try {
        const apiData = await this.getHoroscopeFromAPI(signId);
        this.cacheHoroscope(signId, apiData);
        return apiData;
      } catch (apiError) {
        console.warn('API failed, using fallback:', apiError.message);
        // Return fallback data
        const signSinhala = this.getSignSinhala(signId);
        const fallbackData = this.generateFallbackHoroscope(signSinhala);
        return fallbackData;
      }
    } catch (error) {
      console.error('Error getting horoscope:', error);
      throw error;
    }
  }

  // Fallback personalized guidance generation
  generateFallbackHoroscope(signSinhala) {
    const currentDate = new Date().toLocaleDateString('si-LK', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    });

    const personalizedContent = {
      love: `${currentDate} දිනයේ ඔබට ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ශක්තිමත් දිනයක් වනු ඇත. ඔබේ හදවතේ ඇති ධනාත්මක ශක්තිය අන්‍යයන් වෙත ප්‍රකාශ වනු ඇත. ඔබේ සම්බන්ධතාවල ගැඹුර වැඩි කර ගැනීමට අද හොඳ අවස්ථාවකි.`,
      career: `${currentDate} දිනයේ ඔබට වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව ගුණාංග ප්‍රදර්ශනය කිරීමට හොඳ අවස්ථාවක්. ඔබේ කුසලතා සහ දක්ෂතා අන්‍යයන් විසින් පිළිගනු ලැබේ. ඔබේ වෘත්තීය ඉලක්ක කරා ගමන් කිරීමට අද ප්‍රගතිශීලී පියවරක් ගන්න.`,
      health: `${currentDate} දිනයේ ඔබට ශාරීරික ශක්තිය සහ ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී. ඔබේ සෞඛ්‍යයට අවශ්‍ය සැලකිල්ල ලබා දෙන්න. ඔබේ ශරීරයේ සහ මනසේ සමතුලිතතාවය පවත්වා ගැනීමට අද විශේෂ අවධානය යොමු කරන්න.`,
      finance: `${currentDate} දිනයේ ඔබට මූල්‍ය කටයුතුවල නව ආයෝජන අවස්ථා සොයා ගැනීමට හොඳ කාලයකි. ඔබේ ආර්ථික ස්ථාවරත්වය වැඩි දියුණු වනු ඇත. ඔබේ මූල්‍ය සැලසුම් කිරීමේදී ප්‍රවේශම්කාරී වන්න.`,
      general: `${currentDate} දිනයේ ඔබට සාමාන්‍ය ජීවිතයේ ධනාත්මක ශක්තිය සහ සාර්ථකත්වය අපේක්ෂා කරන්න. ඔබේ අභිලාෂයන් සාක්ෂාත් කර ගැනීමට හොඳ දිනයකි. ඔබේ අභ්‍යන්තර ශක්තිය සහ ප්‍රඥාව මත විශ්වාසය තබන්න.`
    };

    return {
      date: new Date().toLocaleDateString('si-LK'),
      categories: personalizedContent
    };
  }
}

const horoscopeService = new HoroscopeService();
export default horoscopeService;