import React, { useState } from 'react';
import { useAnalytics, useFormTracking, useComponentTracking } from '../hooks/useAnalytics';

// Zodiac signs in Sinhala
const zodiacSigns = [
  { id: 'mesha', name: 'මේෂ (<PERSON><PERSON>)' },
  { id: 'vrushabha', name: 'වෘෂභ (Taurus)' },
  { id: 'mithuna', name: 'මිථුන (<PERSON>)' },
  { id: 'kataka', name: 'කටක (Cancer)' },
  { id: 'simha', name: 'සිංහ (<PERSON>)' },
  { id: 'kanya', name: 'කන්‍යා (Virgo)' },
  { id: 'thula', name: 'තුලා (Libra)' },
  { id: 'vrush<PERSON>ka', name: 'වෘශ්චික (<PERSON><PERSON><PERSON>)' },
  { id: 'dhanu', name: 'ධනු (Sagittarius)' },
  { id: 'makara', name: 'මකර (Capricorn)' },
  { id: 'kumbha', name: 'කුම්භ (Aquarius)' },
  { id: 'meena', name: 'මීන (<PERSON><PERSON><PERSON>)' }
];

const KuberaCheckout = ({ onClose }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [customerInfo, setCustomerInfo] = useState({
    fullName: '',
    phone1: '',
    phone2: '',
    address: '',
    city: '',
    zodiacSign: ''
  });
  const [orderProcessing, setOrderProcessing] = useState(false);
  const [orderId, setOrderId] = useState('');

  // Analytics integration
  const analytics = useAnalytics();
  const formTracking = useFormTracking('kubera_checkout');
  useComponentTracking('KuberaCheckout');

  // Product details
  const product = {
    name: 'කුබේර කාඩ්පත්',
    price: 1299,
    originalPrice: 2599,
    quantity: 1,
    image: '/images/kubera-card-1.png'
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCustomerInfo(prev => ({
      ...prev,
      [name]: value
    }));

    // Track form field interactions
    if (value.trim() !== '') {
      formTracking.trackFieldBlur(name, true);

      // Special tracking for zodiac sign selection
      if (name === 'zodiacSign') {
        analytics.trackEvent('zodiac_selection', {
          event_category: 'form_interaction',
          selected_zodiac: value,
          form_name: 'kubera_checkout'
        });
      }
    }
  };

  const validateStep1 = () => {
    const required = ['fullName', 'phone1', 'address', 'city', 'zodiacSign'];
    return required.every(field => customerInfo[field].trim() !== '');
  };

  const handleNextStep = () => {
    if (currentStep === 1 && validateStep1()) {
      setCurrentStep(2);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePlaceOrder = async () => {
    setOrderProcessing(true);

    // Track checkout step 2 (place order)
    analytics.trackCheckoutStep(2, 'place_order');

    try {
      const orderData = {
        customerInfo,
        items: [{
          name: product.name,
          price: product.price,
          quantity: product.quantity
        }],
        totalAmount: product.price * product.quantity
      };

      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
      const response = await fetch(`${apiUrl}/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData)
      });

      const result = await response.json();

      if (result.success) {
        setOrderId(result.orderId);
        setCurrentStep(3);

        // Track successful purchase
        analytics.trackKuberaCardPurchase({
          transaction_id: result.orderId,
          value: product.price * product.quantity,
          currency: 'LKR',
          items: [{
            item_id: 'kubera_card',
            item_name: product.name,
            item_category: 'spiritual_products',
            price: product.price,
            quantity: product.quantity
          }]
        });

        // Track form submission success
        analytics.trackFormSubmit('kubera_checkout', {
          order_id: result.orderId,
          customer_zodiac: customerInfo.zodiacSign,
          order_value: product.price * product.quantity
        });

      } else {
        alert('ඇණවුම ස්ථාපනය කිරීමේදී දෝෂයක් සිදුවිය. කරුණාකර නැවත උත්සාහ කරන්න.');

        // Track checkout failure
        analytics.trackError('Order placement failed', 'KuberaCheckout', false);
      }
    } catch (error) {
      console.error('Order placement error:', error);
      alert('ඇණවුම ස්ථාපනය කිරීමේදී දෝෂයක් සිදුවිය. කරුණාකර නැවත උත්සාහ කරන්න.');

      // Track checkout error
      analytics.trackError(`Checkout error: ${error.message}`, 'KuberaCheckout', false);
    } finally {
      setOrderProcessing(false);
    }
  };

  const renderStep1 = () => (
    <div className="checkout-step">
      <h3 className="step-title">පාරිභෝගික තොරතුරු</h3>
      
      <div className="form-group">
        <label htmlFor="fullName">සම්පූර්ණ නම *</label>
        <input
          type="text"
          id="fullName"
          name="fullName"
          value={customerInfo.fullName}
          onChange={handleInputChange}
          onFocus={() => formTracking.trackFieldFocus('fullName')}
          onBlur={(e) => formTracking.trackFieldBlur('fullName', e.target.value.trim() !== '')}
          placeholder="ඔබේ සම්පූර්ණ නම ඇතුළත් කරන්න"
          required
        />
      </div>

      <div className="form-group">
        <label htmlFor="phone1">දුරකථන අංකය 1 *</label>
        <input
          type="tel"
          id="phone1"
          name="phone1"
          value={customerInfo.phone1}
          onChange={handleInputChange}
          placeholder="07XXXXXXXX"
          required
        />
      </div>

      <div className="form-group">
        <label htmlFor="phone2">දුරකථන අංකය 2</label>
        <input
          type="tel"
          id="phone2"
          name="phone2"
          value={customerInfo.phone2}
          onChange={handleInputChange}
          placeholder="07XXXXXXXX (විකල්ප)"
        />
      </div>

      <div className="form-group">
        <label htmlFor="zodiacSign">ඔබේ රාශිය *</label>
        <select
          id="zodiacSign"
          name="zodiacSign"
          value={customerInfo.zodiacSign}
          onChange={handleInputChange}
          required
        >
          <option value="">රාශිය තෝරන්න</option>
          {zodiacSigns.map(sign => (
            <option key={sign.id} value={sign.name}>
              {sign.name}
            </option>
          ))}
        </select>
      </div>



      <div className="form-group">
        <label htmlFor="address">ලිපිනය *</label>
        <textarea
          id="address"
          name="address"
          value={customerInfo.address}
          onChange={handleInputChange}
          placeholder="ඔබේ සම්පූර්ණ ලිපිනය ඇතුළත් කරන්න"
          rows="3"
          required
        />
      </div>

      <div className="form-row">
        <div className="form-group">
          <label htmlFor="city">නගරය *</label>
          <input
            type="text"
            id="city"
            name="city"
            value={customerInfo.city}
            onChange={handleInputChange}
            placeholder="නගරය"
            required
          />
        </div>


      </div>

      <div className="step-actions">
        <button className="btn-secondary" onClick={onClose}>
          අවලංගු කරන්න
        </button>
        <button 
          className="btn-primary" 
          onClick={handleNextStep}
          disabled={!validateStep1()}
        >
          ඊළඟ පියවර →
        </button>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="checkout-step">
      <h3 className="step-title">ඇණවුම් සමාලෝචනය</h3>
      
      {/* Order Summary */}
      <div className="order-summary">
        <div className="product-summary">
          <img src={product.image} alt={product.name} className="product-thumb" />
          <div className="product-info">
            <h4>{product.name}</h4>
            <p>ප්‍රමාණය: {product.quantity}</p>
            <div className="price-info">
              <span className="original-price">රු. {product.originalPrice.toLocaleString()}</span>
              <span className="current-price">රු. {product.price.toLocaleString()}</span>
            </div>
          </div>
        </div>

        <div className="order-total">
          <div className="total-row">
            <span>උප එකතුව:</span>
            <span>රු. {(product.price * product.quantity).toLocaleString()}</span>
          </div>
          <div className="total-row">
            <span>ගෙන්වා දීමේ ගාස්තුව:</span>
            <span className="free">නොමිලේ</span>
          </div>
          <div className="total-row final-total">
            <span>මුළු මිල:</span>
            <span>රු. {(product.price * product.quantity).toLocaleString()}</span>
          </div>
        </div>
      </div>

      {/* Customer Info Summary */}
      <div className="customer-summary">
        <h4>ගෙන්වා දීමේ ලිපිනය:</h4>
        <div className="address-info">
          <p><strong>{customerInfo.fullName}</strong></p>
          <p>දුරකථන 1: {customerInfo.phone1}</p>
          {customerInfo.phone2 && <p>දුරකථන 2: {customerInfo.phone2}</p>}
          <p>රාශිය: {customerInfo.zodiacSign}</p>
          <p>{customerInfo.address}</p>
          <p>{customerInfo.city}</p>
        </div>
      </div>

      {/* Payment Method */}
      <div className="payment-method-info">
        <h4>ගෙවීමේ ක්‍රමය:</h4>
        <div className="payment-option">
          <span className="payment-icon">💳</span>
          <span>Cash on Delivery (COD)</span>
          <span className="payment-note">භාණ්ඩ ලැබෙන විට ගෙවන්න</span>
        </div>
      </div>

      <div className="step-actions">
        <button className="btn-secondary" onClick={handlePrevStep}>
          ← ආපසු
        </button>
        <button 
          className="btn-primary" 
          onClick={handlePlaceOrder}
          disabled={orderProcessing}
        >
          {orderProcessing ? 'ඇණවුම ස්ථාපනය කරමින්...' : 'ඇණවුම තහවුරු කරන්න'}
        </button>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="checkout-step success-step">
      <div className="success-icon">✅</div>
      <h3 className="step-title">ඇණවුම සාර්ථකයි!</h3>
      
      <div className="order-confirmation">
        <p className="success-message">
          ඔබේ ඇණවුම සාර්ථකව ස්ථාපනය කර ඇත.
        </p>
        
        <div className="order-details">
          <p><strong>ඇණවුම් අංකය:</strong> #{orderId}</p>
          <p><strong>ගෙන්වා දීමේ කාලය:</strong> 2-3 වැඩ කරන දින</p>
          <p><strong>ගෙවීමේ ක්‍රමය:</strong> Cash on Delivery</p>
        </div>

        <div className="next-steps">
          <h4>ඊළඟ පියවර:</h4>
          <ul>
            <li>අපගේ කණ්ඩායම ඔබ සමඟ දුරකථනයෙන් සම්බන්ධ වනු ඇත</li>
            <li>ඇණවුම් තහවුරු කිරීම සඳහා SMS පණිවිඩයක් ලැබෙනු ඇත</li>
            <li>භාණ්ඩ ගෙන්වා දෙන විට මුදල් ගෙවන්න</li>
          </ul>
        </div>
      </div>

      <div className="step-actions">
        <button className="btn-primary" onClick={onClose}>
          අවසන්
        </button>
      </div>
    </div>
  );

  return (
    <div className="checkout-overlay">
      <div className="checkout-container dark-glass-card">
        <div className="card-glow"></div>
        <div className="card-shine"></div>
        
        {/* Header */}
        <div className="checkout-header">
          <h2>කුබේර කාඩ්පත් මිලදී ගැනීම</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        {/* Progress Steps */}
        <div className="progress-steps">
          <div className={`step ${currentStep >= 1 ? 'active' : ''} ${currentStep > 1 ? 'completed' : ''}`}>
            <span className="step-number">1</span>
            <span className="step-label">තොරතුරු</span>
          </div>
          <div className={`step ${currentStep >= 2 ? 'active' : ''} ${currentStep > 2 ? 'completed' : ''}`}>
            <span className="step-number">2</span>
            <span className="step-label">සමාලෝචනය</span>
          </div>
          <div className={`step ${currentStep >= 3 ? 'active' : ''}`}>
            <span className="step-number">3</span>
            <span className="step-label">තහවුරු කිරීම</span>
          </div>
        </div>

        {/* Step Content */}
        <div className="checkout-content">
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}
        </div>
      </div>
    </div>
  );
};

export default KuberaCheckout;
